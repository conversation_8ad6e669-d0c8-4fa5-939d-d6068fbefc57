/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* Transcription App Styles */
.transcription-app {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.api-token-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.api-token-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #555;
}

.api-token-section input {
  width: 70%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-right: 1rem;
  font-size: 1rem;
}

.api-token-section button {
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.api-token-section button:hover {
  background: #5a67d8;
}

.api-token-section button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.file-upload-section {
  margin-bottom: 2rem;
}

.upload-area {
  border: 3px dashed #cbd5e0;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f7fafc;
}

.upload-area:hover {
  border-color: #667eea;
  background: #edf2f7;
}

.upload-area p {
  font-size: 1.1rem;
  color: #4a5568;
  margin: 0.5rem 0;
}

.warnings {
  margin-bottom: 1.5rem;
}

.warning {
  background: #fef5e7;
  color: #d69e2e;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  border-left: 4px solid #d69e2e;
  margin-bottom: 0.5rem;
}

.progress-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f7fafc;
  border-radius: 8px;
}

.progress-bar {
  width: 100%;
  height: 24px;
  background: #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 12px;
}

.progress-text {
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.progress-percent {
  font-size: 1.2rem;
  font-weight: 700;
  color: #667eea;
  text-align: center;
}

.error-section {
  margin: 1.5rem 0;
}

.error {
  background: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #c53030;
}

.action-buttons {
  margin: 2rem 0;
  text-align: center;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-width: 200px;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.primary-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.results-section {
  margin-top: 2rem;
  padding: 2rem;
  background: #f0fff4;
  border-radius: 8px;
  border: 1px solid #9ae6b4;
}

.results-section h3 {
  color: #22543d;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.download-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.download-buttons button {
  background: #48bb78;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.download-buttons button:hover {
  background: #38a169;
}

.transcription-preview {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.transcription-preview h4 {
  margin-bottom: 1rem;
  color: #2d3748;
}

.transcription-preview pre {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #4a5568;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1.5rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .main-content {
    padding: 1rem;
  }

  .transcription-app {
    padding: 1.5rem;
  }

  .api-token-section input {
    width: 100%;
    margin-bottom: 1rem;
    margin-right: 0;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .download-buttons {
    flex-direction: column;
  }

  .download-buttons button {
    width: 100%;
  }
}
